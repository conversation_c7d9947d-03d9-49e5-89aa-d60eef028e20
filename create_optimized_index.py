#!/usr/bin/env python3
"""
Create Optimized HNSW Index

Creates a high-performance HNSW index optimized for massive databases (37M+ embeddings).
"""

import os
import sys
import logging
import time
import psycopg2
from psycopg2 import Error
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def create_optimized_index():
    """Create an optimized HNSW index for massive databases."""
    
    # Database connection
    db_config = {
        'host': os.getenv('POSTGRES_HOST'),
        'port': os.getenv('POSTGRES_PORT', 5432),
        'database': os.getenv('POSTGRES_DB'),
        'user': os.getenv('POSTGRES_USER'),
        'password': os.getenv('POSTGRES_PASSWORD')
    }
    
    connection = None
    cursor = None
    
    try:
        # Connect to database
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()
        logger.info("Connected to database")
        
        # Set optimizations for index creation
        optimizations = [
            "SET maintenance_work_mem = '4GB'",  # Large memory for index building
            "SET max_parallel_maintenance_workers = 4",  # Parallel index building
            "SET checkpoint_completion_target = 0.9",  # Reduce checkpoint frequency
        ]
        
        for opt in optimizations:
            try:
                cursor.execute(opt)
                logger.info(f"Applied: {opt}")
            except Error as e:
                logger.warning(f"Could not apply {opt}: {e}")
        
        connection.commit()
        
        # Check current indexes
        check_query = """
            SELECT indexname, indexdef
            FROM pg_indexes 
            WHERE tablename = 'emb_1024'
            AND indexdef ILIKE '%hnsw%'
            ORDER BY indexname;
        """
        
        cursor.execute(check_query)
        existing_indexes = cursor.fetchall()
        
        logger.info(f"Current HNSW indexes: {len(existing_indexes)}")
        for idx_name, idx_def in existing_indexes:
            logger.info(f"  - {idx_name}")
        
        # Get table stats
        cursor.execute("SELECT COUNT(*) FROM emb_1024;")
        row_count = cursor.fetchone()[0]
        logger.info(f"Table size: {row_count:,} embeddings")
        
        # Recommend optimal parameters for this size
        if row_count > 30_000_000:
            # For 30M+ embeddings: Higher m for better recall, moderate ef_construction for reasonable build time
            m = 16
            ef_construction = 64
            logger.info("Using parameters optimized for 30M+ embeddings")
        elif row_count > 10_000_000:
            # For 10M+ embeddings
            m = 12
            ef_construction = 80
            logger.info("Using parameters optimized for 10M+ embeddings")
        else:
            # For smaller databases
            m = 8
            ef_construction = 100
            logger.info("Using parameters optimized for smaller databases")
        
        index_name = "emb_1024_hnsw_optimized"
        
        # Check if optimized index already exists
        cursor.execute("""
            SELECT indexname FROM pg_indexes 
            WHERE tablename = 'emb_1024' 
            AND indexname = %s;
        """, (index_name,))
        
        if cursor.fetchone():
            logger.warning(f"Index {index_name} already exists!")
            response = input("Drop and recreate? (yes/no): ")
            if response.lower() in ['yes', 'y']:
                logger.info(f"Dropping existing {index_name}...")
                cursor.execute(f"DROP INDEX {index_name};")
                connection.commit()
                logger.info("Existing index dropped")
            else:
                logger.info("Keeping existing index")
                return
        
        # Create the optimized index
        create_query = f"""
            CREATE INDEX {index_name} 
            ON emb_1024 
            USING hnsw (embedding vector_cosine_ops) 
            WITH (m='{m}', ef_construction='{ef_construction}');
        """
        
        logger.info(f"Creating optimized HNSW index with m={m}, ef_construction={ef_construction}")
        logger.info("⚠️  This will take a VERY long time for 37M+ embeddings (potentially hours)")
        logger.info("⚠️  The database will be under heavy load during index creation")
        
        # Confirm before proceeding
        response = input("Proceed with index creation? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            logger.info("Index creation cancelled")
            return
        
        # Start index creation with timing
        start_time = time.time()
        logger.info("🚀 Starting index creation...")
        
        cursor.execute(create_query)
        connection.commit()
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"✅ Index creation completed in {duration:.1f} seconds ({duration/60:.1f} minutes)")
        
        # Verify the new index
        cursor.execute(check_query)
        final_indexes = cursor.fetchall()
        
        logger.info(f"Final HNSW indexes: {len(final_indexes)}")
        for idx_name, idx_def in final_indexes:
            logger.info(f"  - {idx_name}")
            
            # Extract parameters
            if 'm=' in idx_def:
                m_start = idx_def.find("m='") + 3
                m_end = idx_def.find("'", m_start)
                m_value = idx_def[m_start:m_end]
                
                ef_start = idx_def.find("ef_construction='") + 17
                ef_end = idx_def.find("'", ef_start)
                ef_value = idx_def[ef_start:ef_end]
                
                logger.info(f"    Parameters: m={m_value}, ef_construction={ef_value}")
        
        logger.info("🎉 Optimized index creation successful!")
        logger.info("You can now test vector search performance with the new index")
        
    except Error as e:
        logger.error(f"Database error: {e}")
        if connection:
            connection.rollback()
        sys.exit(1)
        
    except KeyboardInterrupt:
        logger.warning("Index creation interrupted by user")
        if connection:
            connection.rollback()
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
        logger.info("Database connection closed")

def show_current_indexes():
    """Show current HNSW indexes without creating new ones."""
    
    db_config = {
        'host': os.getenv('POSTGRES_HOST'),
        'port': os.getenv('POSTGRES_PORT', 5432),
        'database': os.getenv('POSTGRES_DB'),
        'user': os.getenv('POSTGRES_USER'),
        'password': os.getenv('POSTGRES_PASSWORD')
    }
    
    try:
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()
        
        # Check current indexes
        check_query = """
            SELECT indexname, indexdef
            FROM pg_indexes 
            WHERE tablename = 'emb_1024'
            AND indexdef ILIKE '%hnsw%'
            ORDER BY indexname;
        """
        
        cursor.execute(check_query)
        indexes = cursor.fetchall()
        
        print(f"\nCurrent HNSW indexes on emb_1024: {len(indexes)}")
        print("=" * 50)
        
        for idx_name, idx_def in indexes:
            print(f"📊 {idx_name}")
            
            # Extract parameters
            if 'm=' in idx_def:
                m_start = idx_def.find("m='") + 3
                m_end = idx_def.find("'", m_start)
                m_value = idx_def[m_start:m_end]
                
                ef_start = idx_def.find("ef_construction='") + 17
                ef_end = idx_def.find("'", ef_start)
                ef_value = idx_def[ef_start:ef_end]
                
                print(f"   Parameters: m={m_value}, ef_construction={ef_value}")
            
            print()
        
        # Get table size
        cursor.execute("SELECT COUNT(*) FROM emb_1024;")
        row_count = cursor.fetchone()[0]
        print(f"Table size: {row_count:,} embeddings")
        
        cursor.close()
        connection.close()
        
    except Error as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    print("🔧 HNSW Index Optimizer")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--show":
        show_current_indexes()
    else:
        create_optimized_index()
