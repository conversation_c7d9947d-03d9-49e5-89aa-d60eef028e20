#!/usr/bin/env python3
"""
Drop Corrupted Vector Index

Safely removes the potentially corrupted arctic_hnsw_idx index.
"""

import os
import sys
import logging
import psycopg2
from psycopg2 import Error
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def drop_corrupted_index():
    """Drop the corrupted arctic_hnsw_idx index."""
    
    # Database connection
    db_config = {
        'host': os.getenv('POSTGRES_HOST'),
        'port': os.getenv('POSTGRES_PORT', 5432),
        'database': os.getenv('POSTGRES_DB'),
        'user': os.getenv('POSTGRES_USER'),
        'password': os.getenv('POSTGRES_PASSWORD')
    }
    
    connection = None
    cursor = None
    
    try:
        # Connect to database
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()
        logger.info("Connected to database")
        
        # Check if index exists
        check_query = """
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename = 'emb_1024' 
            AND indexname = 'arctic_hnsw_idx';
        """
        
        cursor.execute(check_query)
        result = cursor.fetchone()
        
        if result:
            logger.info("Found arctic_hnsw_idx index - proceeding to drop it")
            
            # Drop the index
            drop_query = "DROP INDEX IF EXISTS arctic_hnsw_idx;"
            
            logger.info("Dropping arctic_hnsw_idx... (this may take a while)")
            cursor.execute(drop_query)
            connection.commit()
            
            logger.info("✅ Successfully dropped arctic_hnsw_idx index")
            
            # Verify it's gone
            cursor.execute(check_query)
            verify_result = cursor.fetchone()
            
            if verify_result is None:
                logger.info("✅ Verified: Index has been completely removed")
            else:
                logger.warning("⚠️ Index still exists - drop may have failed")
                
        else:
            logger.info("arctic_hnsw_idx index not found - nothing to drop")
        
        # Show remaining indexes
        remaining_query = """
            SELECT indexname, indexdef
            FROM pg_indexes 
            WHERE tablename = 'emb_1024'
            AND indexdef ILIKE '%hnsw%'
            ORDER BY indexname;
        """
        
        cursor.execute(remaining_query)
        remaining_indexes = cursor.fetchall()
        
        logger.info(f"Remaining HNSW indexes on emb_1024: {len(remaining_indexes)}")
        for idx_name, idx_def in remaining_indexes:
            logger.info(f"  - {idx_name}")
            
            # Extract parameters
            if 'm=' in idx_def:
                m_start = idx_def.find("m='") + 3
                m_end = idx_def.find("'", m_start)
                m_value = idx_def[m_start:m_end]
                
                ef_start = idx_def.find("ef_construction='") + 17
                ef_end = idx_def.find("'", ef_start)
                ef_value = idx_def[ef_start:ef_end]
                
                logger.info(f"    Parameters: m={m_value}, ef_construction={ef_value}")
        
        if remaining_indexes:
            logger.info("✅ You still have other HNSW indexes available for vector search")
        else:
            logger.warning("⚠️ No HNSW indexes remaining - vector search will be very slow")
            logger.info("Consider creating a new index:")
            logger.info("  CREATE INDEX ON emb_1024 USING hnsw (embedding vector_cosine_ops) WITH (m='16', ef_construction='64');")
        
    except Error as e:
        logger.error(f"Database error: {e}")
        if connection:
            connection.rollback()
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
        logger.info("Database connection closed")

if __name__ == "__main__":
    print("🗑️  Dropping Corrupted Vector Index")
    print("=" * 50)
    
    # Confirm with user
    response = input("Are you sure you want to drop the arctic_hnsw_idx index? (yes/no): ")
    
    if response.lower() in ['yes', 'y']:
        drop_corrupted_index()
    else:
        print("Operation cancelled")
        sys.exit(0)
