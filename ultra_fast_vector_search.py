#!/usr/bin/env python3
"""
Ultra-Fast Vector Search - Optimized for Massive Databases

Specifically optimized for 37M+ embedding databases with aggressive performance tuning.
Uses advanced PostgreSQL optimizations and HNSW parameter tuning.
"""

import os
import sys
import logging
import argparse
import time
from typing import List, Dict, Optional
import psycopg2
from psycopg2 import Error
from dotenv import load_dotenv
import ollama

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class UltraFastVectorSearch:
    """Ultra-optimized vector search for massive databases (37M+ embeddings)."""

    def __init__(self, embedding_model: str = "snowflake-arctic-embed2:latest"):
        self.embedding_model = embedding_model
        self.connection = None
        self.cursor = None
        
        # Database connection
        self.db_config = {
            'host': os.getenv('POSTGRES_HOST'),
            'port': os.getenv('POSTGRES_PORT', 5432),
            'database': os.getenv('POSTGRES_DB'),
            'user': os.getenv('POSTGRES_USER'),
            'password': os.getenv('POSTGRES_PASSWORD')
        }

    def connect(self, ef_search: int = 200):
        """Connect with safe optimizations for massive databases."""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.cursor = self.connection.cursor()

            # Safe optimizations that don't require server restart
            safe_optimizations = [
                "SET work_mem = '1GB'",  # Large memory for sorting
                "SET random_page_cost = 1.0",  # SSD optimization
                "SET effective_cache_size = '8GB'",  # Large cache assumption
                f"SET hnsw.ef_search = {ef_search}",  # HNSW search parameter
                "SET statement_timeout = '300s'",  # 5-minute timeout
            ]

            for opt in safe_optimizations:
                try:
                    self.cursor.execute(opt)
                    logger.debug(f"Applied: {opt}")
                except Error as e:
                    logger.warning(f"Could not apply {opt}: {e}")

            # Commit the settings
            self.connection.commit()

            logger.info(f"Connected with optimizations (ef_search={ef_search})")

        except Error as e:
            logger.error(f"Connection error: {e}")
            raise

    def disconnect(self):
        """Clean disconnect."""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()

    def get_embedding(self, text: str) -> List[float]:
        """Get embedding from Ollama with timing."""
        start_time = time.time()
        try:
            response = ollama.embeddings(model=self.embedding_model, prompt=text)
            embedding = response['embedding']
            embedding_time = time.time() - start_time
            logger.info(f"Embedding generated in {embedding_time:.3f}s ({len(embedding)}D)")
            return embedding
        except Exception as e:
            logger.error(f"Embedding error: {e}")
            raise

    def search_ultra_fast(self, question: str, limit: int = 20, model_id: Optional[int] = None) -> List[Dict]:
        """Ultra-fast vector search with model filtering."""
        
        total_start = time.time()
        
        # Get embedding
        embedding_start = time.time()
        embedding = self.get_embedding(question)
        embedding_time = time.time() - embedding_start
        
        # Convert to PostgreSQL vector format
        embedding_str = '[' + ','.join(map(str, embedding)) + ']'
        
        # Build optimized query
        search_start = time.time()
        
        if model_id:
            # Query with model filtering
            query = """
                SELECT 
                    chunk_id,
                    embedding <=> %s::vector as distance
                FROM emb_1024
                WHERE model_id = %s
                ORDER BY embedding <=> %s::vector
                LIMIT %s;
            """
            params = (embedding_str, model_id, embedding_str, limit)
            logger.info(f"Searching {limit} results with model_id={model_id}...")
        else:
            # Query without model filtering (faster)
            query = """
                SELECT 
                    chunk_id,
                    embedding <=> %s::vector as distance
                FROM emb_1024
                ORDER BY embedding <=> %s::vector
                LIMIT %s;
            """
            params = (embedding_str, embedding_str, limit)
            logger.info(f"Searching {limit} results across all models...")
        
        # Execute search
        self.cursor.execute(query, params)
        results = self.cursor.fetchall()
        search_time = time.time() - search_start
        
        # Format results
        formatted_results = []
        for row in results:
            chunk_id, distance = row
            formatted_results.append({
                'chunk_id': chunk_id,
                'distance': float(distance),
                'similarity_score': float(1 - distance)
            })
        
        total_time = time.time() - total_start
        
        logger.info(f"Search completed in {total_time:.3f}s")
        logger.info(f"  Embedding: {embedding_time:.3f}s")
        logger.info(f"  Vector search: {search_time:.3f}s")
        
        return formatted_results

    def search_with_metadata(self, question: str, limit: int = 20, model_id: Optional[int] = None) -> List[Dict]:
        """Two-stage search: ultra-fast vector first, then metadata."""
        
        # Stage 1: Get top chunk IDs (ultra-fast)
        vector_results = self.search_ultra_fast(question, limit, model_id)
        
        if not vector_results:
            return []
        
        # Stage 2: Get metadata efficiently
        metadata_start = time.time()
        chunk_ids = [r['chunk_id'] for r in vector_results]
        distance_map = {r['chunk_id']: r for r in vector_results}
        
        # Efficient metadata query using ANY array
        metadata_query = """
            SELECT 
                c.id as chunk_id,
                c.document_id,
                d.title as document_title,
                c.text as chunk_text
            FROM chunks c
            JOIN document d ON c.document_id = d.id
            WHERE c.id = ANY(%s)
        """
        
        self.cursor.execute(metadata_query, (chunk_ids,))
        metadata_results = self.cursor.fetchall()
        metadata_time = time.time() - metadata_start
        
        # Combine results maintaining order
        final_results = []
        metadata_dict = {row[0]: row for row in metadata_results}
        
        for chunk_id in chunk_ids:
            if chunk_id in metadata_dict:
                row = metadata_dict[chunk_id]
                vector_data = distance_map[chunk_id]
                
                final_results.append({
                    'chunk_id': chunk_id,
                    'document_id': row[1],
                    'document_title': row[2],
                    'chunk_text': row[3],
                    'distance': vector_data['distance'],
                    'similarity_score': vector_data['similarity_score']
                })
        
        logger.info(f"Metadata lookup: {metadata_time:.3f}s")
        return final_results

    def benchmark_search(self, question: str, ef_search_values: List[int] = [50, 100, 200, 400]) -> Dict:
        """Benchmark different ef_search values."""
        results = {}
        
        for ef_search in ef_search_values:
            logger.info(f"\n=== Testing ef_search = {ef_search} ===")
            
            # Reconnect with new ef_search
            self.disconnect()
            self.connect(ef_search=ef_search)
            
            # Run search 3 times and take average
            times = []
            for i in range(3):
                start_time = time.time()
                search_results = self.search_ultra_fast(question, limit=10)
                search_time = time.time() - start_time
                times.append(search_time)
                logger.info(f"  Run {i+1}: {search_time:.3f}s")
            
            avg_time = sum(times) / len(times)
            results[ef_search] = {
                'avg_time': avg_time,
                'times': times,
                'result_count': len(search_results)
            }
            
            logger.info(f"  Average: {avg_time:.3f}s")
        
        return results

    def check_indexes(self):
        """Check available HNSW indexes."""
        try:
            query = """
                SELECT indexname, indexdef
                FROM pg_indexes 
                WHERE tablename = 'emb_1024'
                AND indexdef ILIKE '%hnsw%'
                ORDER BY indexname;
            """
            
            self.cursor.execute(query)
            indexes = self.cursor.fetchall()
            
            logger.info(f"Found {len(indexes)} HNSW indexes:")
            for idx_name, idx_def in indexes:
                logger.info(f"  {idx_name}")
                # Extract parameters
                if 'm=' in idx_def:
                    m_start = idx_def.find("m='") + 3
                    m_end = idx_def.find("'", m_start)
                    m_value = idx_def[m_start:m_end]
                    
                    ef_start = idx_def.find("ef_construction='") + 17
                    ef_end = idx_def.find("'", ef_start)
                    ef_value = idx_def[ef_start:ef_end]
                    
                    logger.info(f"    Parameters: m={m_value}, ef_construction={ef_value}")
            
            return len(indexes) > 0
            
        except Error as e:
            logger.error(f"Error checking indexes: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='Ultra-fast vector search for massive databases')
    parser.add_argument('question', type=str, help='Search question')
    parser.add_argument('--limit', type=int, default=20, help='Number of results')
    parser.add_argument('--model-id', type=int, help='Filter by specific model ID')
    parser.add_argument('--ef-search', type=int, default=200, help='HNSW ef_search parameter')
    parser.add_argument('--vector-only', action='store_true', help='Vector search only (no metadata)')
    parser.add_argument('--benchmark', action='store_true', help='Benchmark different ef_search values')
    parser.add_argument('--check-indexes', action='store_true', help='Check HNSW indexes')
    parser.add_argument('--show-text', action='store_true', help='Show chunk text in results')
    
    args = parser.parse_args()
    
    searcher = UltraFastVectorSearch()
    
    try:
        searcher.connect(ef_search=args.ef_search)
        
        if args.check_indexes:
            searcher.check_indexes()
            return
        
        if args.benchmark:
            results = searcher.benchmark_search(args.question)
            print(f"\n{'='*60}")
            print("BENCHMARK RESULTS")
            print(f"{'='*60}")
            for ef_search, data in results.items():
                print(f"ef_search={ef_search}: {data['avg_time']:.3f}s average")
            return
        
        # Perform search
        if args.vector_only:
            results = searcher.search_ultra_fast(args.question, args.limit, args.model_id)
            
            print(f"\n{'='*60}")
            print(f"ULTRA-FAST VECTOR SEARCH RESULTS ({len(results)})")
            print(f"{'='*60}")
            
            for i, result in enumerate(results, 1):
                print(f"{i:2d}. Chunk ID: {result['chunk_id']} | "
                      f"Similarity: {result['similarity_score']:.4f} | "
                      f"Distance: {result['distance']:.6f}")
        
        else:
            results = searcher.search_with_metadata(args.question, args.limit, args.model_id)
            
            print(f"\n{'='*60}")
            print(f"ULTRA-FAST SEARCH RESULTS ({len(results)})")
            print(f"{'='*60}")
            
            for i, result in enumerate(results, 1):
                print(f"\n{i:2d}. Document ID: {result['document_id']} | "
                      f"Similarity: {result['similarity_score']:.4f}")
                print(f"    Title: {result['document_title']}")
                
                if args.show_text and result['chunk_text']:
                    text_preview = result['chunk_text'][:200].replace('\n', ' ')
                    print(f"    Text: {text_preview}...")
        
        # Summary
        if results:
            best_score = max(r['similarity_score'] for r in results)
            worst_score = min(r['similarity_score'] for r in results)
            print(f"\nSimilarity range: {worst_score:.4f} - {best_score:.4f}")
            
            if not args.vector_only:
                unique_docs = len(set(r['document_id'] for r in results))
                print(f"Unique documents: {unique_docs}")
        
    except Exception as e:
        logger.error(f"Search failed: {e}")
        sys.exit(1)
    finally:
        searcher.disconnect()

if __name__ == "__main__":
    main()
